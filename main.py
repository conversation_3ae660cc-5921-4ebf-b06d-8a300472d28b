import logging
import sys
import os
from pathlib import Path
import PyPDF2
from io import BytesIO
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("weaviate_module/logs/pdf_insert.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("pdf_insert")
# Import your existing modules
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)
try:
    from make_connection import get_sync_client
    from schema import get_or_create_collection
    logger.info("Successfully imported modules")
except Exception as e:
    logger.error(f"Error importing modules: {str(e)}")
    raise
def extract_text_from_pdf(pdf_path):
    """
    Extract text from all pages of a PDF file
    Args:
        pdf_path: Path to the PDF file
    Returns:
        List of strings, each representing text from one page
    """
    pages_text = []
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            total_pages = len(pdf_reader.pages)
            logger.info(f"PDF has {total_pages} pages")
            for page_num in range(total_pages):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                # Clean up the text (remove excessive whitespace)
                cleaned_text = ' '.join(text.split())
                if cleaned_text.strip():  # Only add non-empty pages
                    pages_text.append(cleaned_text)
                    logger.info(f"Extracted text from page {page_num + 1} ({len(cleaned_text)} characters)")
                else:
                    logger.warning(f"Page {page_num + 1} appears to be empty or contains no extractable text")
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {str(e)}")
        return None
    return pages_text
def insert_pdf_pages_to_weaviate(pdf_path, collection_name):
    """
    Read PDF and insert each page as a data point in Weaviate
    Args:
        pdf_path: Path to the PDF file
        collection_name: Name of the Weaviate collection
    Returns:
        Boolean indicating success/failure
    """
    logger.info(f"Starting PDF insertion process for: {pdf_path}")
    # Extract text from PDF
    pages_text = extract_text_from_pdf(pdf_path)
    if not pages_text:
        logger.error("Failed to extract text from PDF")
        return False
    # Get or create collection
    collection = get_or_create_collection(collection_name)
    if not collection:
        logger.error("Failed to get or create collection")
        return False
    # Insert each page
    successful_inserts = 0
    pdf_filename = Path(pdf_path).name
    try:
        for page_num, page_text in enumerate(pages_text, 1):
            # Create data object for this page
            data_object = {
                "data": page_text,
                "source_file": pdf_filename,
                "page_number": page_num,
                "total_pages": len(pages_text)
            }
            # Insert into Weaviate
            result = collection.data.insert(data_object)
            if result:
                successful_inserts += 1
                logger.info(f"Successfully inserted page {page_num}/{len(pages_text)}")
            else:
                logger.error(f"Failed to insert page {page_num}")
    except Exception as e:
        logger.error(f"Error during batch insertion: {str(e)}")
        return False
    logger.info(f"Insertion completed: {successful_inserts}/{len(pages_text)} pages inserted successfully")
    return successful_inserts == len(pages_text)
def main():
    """
    Main function to run the PDF insertion process
    """
    # Configuration
    PDF_PATH = "D:/pdf/Bangla_sohopat_class_9_PDF_Web.pdf"  # Update this path
    COLLECTION_NAME = "Bangla_sohopat_class_9"  # Update collection name as needed
    # Validate PDF file exists
    if not os.path.exists(PDF_PATH):
        logger.error(f"PDF file not found: {PDF_PATH}")
        return
    # Run insertion
    success = insert_pdf_pages_to_weaviate(PDF_PATH, COLLECTION_NAME)
    if success:
        logger.info("PDF insertion process completed successfully!")
    else:
        logger.error("PDF insertion process failed!")
if __name__ == "__main__":
    main()